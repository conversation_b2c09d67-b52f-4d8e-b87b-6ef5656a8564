-- Migration: Default Agent MCP Refactor
-- Description: Migrate Composio MCP storage from mcp_oauth_tokens to default agent custom_mcps
-- This enables centralized MCP management through default agents

BEGIN;

-- Function to ensure each account has a default agent
CREATE OR REPLACE FUNCTION ensure_default_agent_for_account(p_account_id UUID)
RETURNS UUID AS $$
DECLARE
    v_default_agent_id UUID;
    v_user_id UUID;
BEGIN
    -- Check if account already has a default agent
    SELECT agent_id INTO v_default_agent_id
    FROM agents 
    WHERE account_id = p_account_id AND is_default = true
    LIMIT 1;
    
    -- If no default agent exists, create one
    IF v_default_agent_id IS NULL THEN
        -- Get the account owner's user_id for proper attribution
        SELECT user_id INTO v_user_id
        FROM basejump.accounts 
        WHERE id = p_account_id
        LIMIT 1;
        
        -- Create default agent
        INSERT INTO agents (
            account_id,
            name,
            description,
            system_prompt,
            configured_mcps,
            custom_mcps,
            agentpress_tools,
            is_default,
            avatar,
            avatar_color
        ) VALUES (
            p_account_id,
            'Atlas Agent',
            'Your default Atlas agent with centralized tool configurations',
            'You are Atlas Agent, a helpful AI assistant with access to various tools and integrations. Provide clear, accurate, and helpful responses to user queries.',
            '[]'::jsonb,
            '[]'::jsonb,
            '{}'::jsonb,
            true,
            '🤖',
            '#6366f1'
        ) RETURNING agent_id INTO v_default_agent_id;
        
        RAISE NOTICE 'Created default agent % for account %', v_default_agent_id, p_account_id;
    END IF;
    
    RETURN v_default_agent_id;
END;
$$ LANGUAGE plpgsql;

-- Function to migrate Composio MCP tokens to default agent custom_mcps
CREATE OR REPLACE FUNCTION migrate_composio_mcps_to_default_agent()
RETURNS INTEGER AS $$
DECLARE
    v_token_record RECORD;
    v_account_id UUID;
    v_default_agent_id UUID;
    v_current_custom_mcps JSONB;
    v_new_mcp_config JSONB;
    v_app_key TEXT;
    v_migrated_count INTEGER := 0;
BEGIN
    -- Loop through all Composio MCP tokens that haven't been migrated
    FOR v_token_record IN 
        SELECT * FROM mcp_oauth_tokens 
        WHERE qualified_name LIKE 'composio/%' 
        AND migrated_to_default_agent = false
    LOOP
        -- Get account_id from user_id
        SELECT basejump.get_account_id(v_token_record.user_id) INTO v_account_id;
        
        IF v_account_id IS NULL THEN
            RAISE NOTICE 'Could not find account for user %, skipping token %', v_token_record.user_id, v_token_record.id;
            CONTINUE;
        END IF;
        
        -- Ensure default agent exists for this account
        v_default_agent_id := ensure_default_agent_for_account(v_account_id);
        
        -- Extract app_key from qualified_name (composio/gmail -> gmail)
        v_app_key := REPLACE(v_token_record.qualified_name, 'composio/', '');
        
        -- Get current custom_mcps for the default agent
        SELECT custom_mcps INTO v_current_custom_mcps
        FROM agents 
        WHERE agent_id = v_default_agent_id;
        
        -- Create new MCP config for Composio
        v_new_mcp_config := jsonb_build_object(
            'name', INITCAP(v_app_key),
            'type', 'composio',
            'config', jsonb_build_object(
                'app_key', v_app_key,
                'mcp_url', v_token_record.access_token,
                'session_uuid', v_token_record.refresh_token,
                'qualified_name', v_token_record.qualified_name
            ),
            'enabledTools', '[]'::jsonb,
            'migrated_from_oauth_tokens', true,
            'migrated_at', NOW()
        );
        
        -- Add to existing custom_mcps array
        v_current_custom_mcps := v_current_custom_mcps || jsonb_build_array(v_new_mcp_config);
        
        -- Update the default agent with new custom_mcps
        UPDATE agents 
        SET custom_mcps = v_current_custom_mcps,
            updated_at = NOW()
        WHERE agent_id = v_default_agent_id;
        
        -- Mark the token as migrated
        UPDATE mcp_oauth_tokens 
        SET migrated_to_default_agent = true,
            updated_at = NOW()
        WHERE id = v_token_record.id;
        
        v_migrated_count := v_migrated_count + 1;
        
        RAISE NOTICE 'Migrated Composio MCP % for account % to default agent %', 
                     v_app_key, v_account_id, v_default_agent_id;
    END LOOP;
    
    RETURN v_migrated_count;
END;
$$ LANGUAGE plpgsql;

-- Function to get default agent for account (with auto-creation)
CREATE OR REPLACE FUNCTION get_or_create_default_agent(p_account_id UUID)
RETURNS UUID AS $$
DECLARE
    v_default_agent_id UUID;
BEGIN
    v_default_agent_id := ensure_default_agent_for_account(p_account_id);
    RETURN v_default_agent_id;
END;
$$ LANGUAGE plpgsql;

-- Add comment to custom_mcps column to document Composio support
COMMENT ON COLUMN agents.custom_mcps IS 'Stores custom MCP server configurations including Composio MCPs. Format: [{"name": "Gmail", "type": "composio", "config": {"app_key": "gmail", "mcp_url": "...", "session_uuid": "..."}, "enabledTools": [...]}]';

-- Run the migration for existing data
SELECT migrate_composio_mcps_to_default_agent() AS migrated_count;

COMMIT;
